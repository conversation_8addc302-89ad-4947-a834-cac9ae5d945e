from flask import Flask, render_template, request, redirect, flash, jsonify
import json
import os
import requests
from dotenv import load_dotenv
from datetime import datetime
import uuid

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'fallback-secret-for-dev-only')  # For flash messages

# BaaS API Configuration for v4
BLOCKAPI_BASE_URL = os.getenv("BLOCKAPI_BASE_URL", "https://blockapi.co.za/api/v1")
BLOCKAPI_API_KEY = os.getenv("BLOCKAPI_API_KEY")
TENANT_ID = os.getenv("BLOCKAPI_TENANT_ID")
WEBHOOK_URL = os.getenv("WEBHOOK_URL")  # Your app's webhook endpoint URL

# Validate required environment variables
if not BLOCKAPI_API_KEY:
    raise ValueError("BLOCKAPI_API_KEY environment variable is required")
if not TENANT_ID:
    raise ValueError("BLOCKAPI_TENANT_ID environment variable is required")
if not WEBHOOK_URL:
    raise ValueError("WEBHOOK_URL environment variable is required")

# In-memory storage for pending songs (use database in production)
pending_songs = {}  # Format: {song_id: {song_data, timestamp, status}}
confirmed_songs = []  # Cache for confirmed songs from BaaS

# Helper functions for song management
def generate_song_id():
    """Generate unique song ID"""
    return str(uuid.uuid4())

def add_pending_song(song_data):
    """Add a song to pending storage"""
    song_id = generate_song_id()
    pending_songs[song_id] = {
        'id': song_id,
        'data': song_data,
        'timestamp': datetime.now().isoformat(),
        'status': 'pending',
        'blockchain_tx_id': None
    }
    return song_id

def update_song_status(song_id, status, blockchain_tx_id=None):
    """Update song status when webhook is received"""
    if song_id in pending_songs:
        pending_songs[song_id]['status'] = status
        if blockchain_tx_id:
            pending_songs[song_id]['blockchain_tx_id'] = blockchain_tx_id

        # If confirmed, move to confirmed_songs and remove from pending
        if status == 'confirmed':
            confirmed_song = pending_songs[song_id]
            confirmed_songs.append(confirmed_song)
            del pending_songs[song_id]

def get_all_songs():
    """Get all songs (pending + confirmed) for display"""
    all_songs = []

    # Add confirmed songs
    for song in confirmed_songs:
        all_songs.append({
            'id': song['id'],
            'title': song['data']['title'],
            'url': song['data']['url'],
            'price': song['data']['price'],
            'owner': song['data']['owner'],
            'status': 'confirmed',
            'tx_id': song.get('blockchain_tx_id', ''),
            'timestamp': song['timestamp']
        })

    # Add pending songs
    for song_id, song in pending_songs.items():
        all_songs.append({
            'id': song['id'],
            'title': song['data']['title'],
            'url': song['data']['url'],
            'price': song['data']['price'],
            'owner': song['data']['owner'],
            'status': song['status'],
            'tx_id': song.get('blockchain_tx_id', ''),
            'timestamp': song['timestamp']
        })

    # Sort by timestamp (most recent first)
    all_songs.sort(key=lambda x: x['timestamp'], reverse=True)
    return all_songs


@app.route("/")
def index():
    """View all registered songs (confirmed + pending)"""
    try:
        # Get all songs from our storage system
        songs = get_all_songs()

        # Add display-friendly status information
        for song in songs:
            if song['status'] == 'pending':
                song['status_display'] = '⏳ Pending'
                song['status_class'] = 'status-pending'
            elif song['status'] == 'confirmed':
                song['status_display'] = '✅ Confirmed'
                song['status_class'] = 'status-confirmed'
            elif song['status'] == 'failed':
                song['status_display'] = '❌ Failed'
                song['status_class'] = 'status-failed'
            else:
                song['status_display'] = '🔄 Processing'
                song['status_class'] = 'status-processing'

        return render_template("index.html", songs=songs)

    except Exception as e:
        flash(f"Error retrieving songs: {e}", "error")
        return render_template("index.html", songs=[])


def send_to_baas_api(song_data):
    """Send song data to BaaS API"""
    payload = {
        "tenant_id": TENANT_ID,
        "application": "songRegistry",
        "version": 4,
        "data": song_data,
        "webhook_url": WEBHOOK_URL,
        "metadata": {
            "timestamp": datetime.now().isoformat(),
            "source": "song-registry-v4"
        }
    }

    headers = {
        "Authorization": f"Bearer {BLOCKAPI_API_KEY}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(
            f"{BLOCKAPI_BASE_URL}/blockchain/submit",
            json=payload,
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"BaaS API error: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        raise Exception(f"Network error calling BaaS API: {str(e)}")

@app.route("/register_song", methods=["POST"])
def register_song():
    try:
        title = request.form.get("title")
        url = request.form.get("url")
        price = int(request.form.get("price"))

        # Validate input
        if not title or not url:
            flash("Title and URL are required", "error")
            return redirect("/")

        # Build song data for BaaS API
        song_data = {
            "title": title,
            "url": url,
            "price": price,
            "owner": "song-registry-user",  # Replace with actual user identification
            "registration_timestamp": datetime.now().isoformat()
        }

        # Add to pending storage first
        song_id = add_pending_song(song_data)

        # Send to BaaS API
        baas_response = send_to_baas_api(song_data)

        # Update pending song with BaaS reference
        if song_id in pending_songs:
            pending_songs[song_id]['baas_reference'] = baas_response.get('reference_id')
            pending_songs[song_id]['baas_status'] = baas_response.get('status', 'queued')

        flash(f"Song registration submitted! Reference ID: {baas_response.get('reference_id', song_id)}", "success")
        flash("Your song is being processed and will appear as confirmed once written to the blockchain.", "info")

    except Exception as e:
        # If we created a pending song but API failed, mark it as failed
        if 'song_id' in locals() and song_id in pending_songs:
            pending_songs[song_id]['status'] = 'failed'
            pending_songs[song_id]['error'] = str(e)

        flash(f"Error registering song: {e}", "error")

    return redirect("/")    # back to the landing page


@app.route("/webhook/blockchain-notification", methods=["POST"])
def blockchain_webhook():
    """Receive notifications from BaaS platform when blockchain write completes"""
    try:
        # Verify the request is from BaaS platform (implement proper authentication)
        webhook_data = request.get_json()

        if not webhook_data:
            return jsonify({"error": "No JSON data received"}), 400

        # Extract notification details
        reference_id = webhook_data.get('reference_id')
        status = webhook_data.get('status')  # 'confirmed', 'failed', etc.
        blockchain_tx_id = webhook_data.get('blockchain_tx_id')
        error_message = webhook_data.get('error_message')

        if not reference_id:
            return jsonify({"error": "Missing reference_id"}), 400

        # Find the pending song by BaaS reference ID
        song_id = None
        for sid, song in pending_songs.items():
            if song.get('baas_reference') == reference_id:
                song_id = sid
                break

        if not song_id:
            # Log this - might be a delayed notification for already processed song
            app.logger.warning(f"Received webhook for unknown reference_id: {reference_id}")
            return jsonify({"message": "Reference ID not found, but acknowledged"}), 200

        # Update song status based on notification
        if status == 'confirmed':
            update_song_status(song_id, 'confirmed', blockchain_tx_id)
            app.logger.info(f"Song {song_id} confirmed on blockchain: {blockchain_tx_id}")
        elif status == 'failed':
            pending_songs[song_id]['status'] = 'failed'
            pending_songs[song_id]['error'] = error_message or 'Blockchain write failed'
            app.logger.error(f"Song {song_id} failed: {error_message}")
        else:
            # Handle other statuses (processing, etc.)
            pending_songs[song_id]['baas_status'] = status
            app.logger.info(f"Song {song_id} status updated: {status}")

        return jsonify({
            "message": "Webhook processed successfully",
            "reference_id": reference_id,
            "status": status
        }), 200

    except Exception as e:
        app.logger.error(f"Webhook processing error: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/songs/status/<song_id>", methods=["GET"])
def get_song_status(song_id):
    """API endpoint to check song registration status"""
    try:
        # Check pending songs
        if song_id in pending_songs:
            song = pending_songs[song_id]
            return jsonify({
                "song_id": song_id,
                "status": song['status'],
                "baas_status": song.get('baas_status'),
                "baas_reference": song.get('baas_reference'),
                "blockchain_tx_id": song.get('blockchain_tx_id'),
                "timestamp": song['timestamp'],
                "error": song.get('error')
            })

        # Check confirmed songs
        for song in confirmed_songs:
            if song['id'] == song_id:
                return jsonify({
                    "song_id": song_id,
                    "status": "confirmed",
                    "blockchain_tx_id": song.get('blockchain_tx_id'),
                    "timestamp": song['timestamp']
                })

        return jsonify({"error": "Song not found"}), 404

    except Exception as e:
        return jsonify({"error": str(e)}), 500


# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('error.html', error_code=404, error_message="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error_code=500, error_message="Internal server error"), 500

# Health check endpoint
@app.route("/health")
def health_check():
    """Health check endpoint for monitoring"""
    return jsonify({
        "status": "healthy",
        "version": "4.0",
        "pending_songs": len(pending_songs),
        "confirmed_songs": len(confirmed_songs)
    })

if __name__ == "__main__":
    # Validate environment on startup
    try:
        if not BLOCKAPI_API_KEY:
            print("WARNING: BLOCKAPI_API_KEY not set")
        if not TENANT_ID:
            print("WARNING: BLOCKAPI_TENANT_ID not set")
        if not WEBHOOK_URL:
            print("WARNING: WEBHOOK_URL not set")

        print(f"Starting Song Registry v4 with BaaS integration...")
        print(f"BaaS API: {BLOCKAPI_BASE_URL}")
        print(f"Webhook URL: {WEBHOOK_URL}")

    except Exception as e:
        print(f"Startup error: {e}")

    app.run(debug=False, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
