from flask import Flask, render_template, request, redirect, flash, jsonify
import json
import os
import requests
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'fallback-secret-for-dev-only')

# BaaS API Configuration
BLOCKAPI_BASE_URL = os.getenv("BLOCKAPI_BASE_URL", "https://blockapi.co.za/api/v1")
BLOCKAPI_API_KEY = os.getenv("BLOCKAPI_API_KEY")
TENANT_ID = os.getenv("BLOCKAPI_TENANT_ID")
WEBHOOK_URL = os.getenv("WEBHOOK_URL")

# Simple storage for songs (in production, use a database)
songs = []




@app.route("/")
def index():
    """View all registered songs"""
    return render_template("index.html", songs=songs)


@app.route("/register_song", methods=["POST"])
def register_song():
    try:
        title = request.form.get("title")
        url = request.form.get("url")
        price = int(request.form.get("price"))

        # Build song data for BaaS API
        song_data = {
            "title": title,
            "url": url,
            "price": price,
            "owner": "song-registry-user"
        }

        # Prepare BaaS API payload
        payload = {
            "tenant_id": TENANT_ID,
            "application": "songRegistry",
            "version": 4,
            "data": song_data,
            "webhook_url": WEBHOOK_URL
        }

        headers = {
            "Authorization": f"Bearer {BLOCKAPI_API_KEY}",
            "Content-Type": "application/json"
        }

        # Send to BaaS API
        response = requests.post(
            f"{BLOCKAPI_BASE_URL}/blockchain/submit",
            json=payload,
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            # Add to local storage for display
            song_data['id'] = len(songs) + 1
            songs.append(song_data)
            flash("Song registered successfully! It will be written to the blockchain.", "success")
        else:
            flash(f"Error: {response.status_code} - {response.text}", "error")

    except Exception as e:
        flash(f"Error registering song: {e}", "error")

    return redirect("/")


@app.route("/webhook/blockchain-notification", methods=["POST"])
def blockchain_webhook():
    """Simple webhook to receive BaaS notifications"""
    try:
        webhook_data = request.get_json()

        # Just log the notification for now
        print(f"Received webhook: {webhook_data}")

        return jsonify({"message": "Webhook received"}), 200

    except Exception as e:
        print(f"Webhook error: {str(e)}")
        return jsonify({"error": "Webhook processing failed"}), 500


if __name__ == "__main__":
    app.run(debug=False, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
