# Song Registry v4 - BaaS Integration Environment Variables

# Flask Configuration
FLASK_SECRET_KEY=your-secret-key-here

# BaaS API Configuration (blockapi.co.za)
BLOCKAPI_BASE_URL=https://blockapi.co.za/api/v1
BLOCKAPI_API_KEY=your-baas-api-key-here
BLOCKAPI_TENANT_ID=your-tenant-id-here

# Webhook Configuration
# This should be your publicly accessible app URL + /webhook/blockchain-notification
WEBHOOK_URL=https://your-app-domain.com/webhook/blockchain-notification

# Example for local development (use ngrok or similar for testing)
# WEBHOOK_URL=https://abc123.ngrok.io/webhook/blockchain-notification
